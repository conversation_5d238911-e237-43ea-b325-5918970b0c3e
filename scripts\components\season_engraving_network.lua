-- 季节刻印网络同步代理组件
-- 挂载在forest_network上，为玩家的season_engraving组件提供网络同步服务
-- 符合DST最佳实践：网络变量挂载在forest_network而不是玩家实体上

local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local AllPlayers = _G.AllPlayers
local net_string = _G.net_string

local SeasonEngravingNetwork = Class(function(self, inst)
    self.inst = inst
    self.player_seasons = {} -- 存储每个玩家的季节状态
    self.player_manual_seasons = {} -- 存储每个玩家的手动季节状态
    self._network_initialized = false
    
    -- 验证实体是否为forest_network
    if not inst.GUID or not inst.entity then
        print("[SeasonWorkshop] Error: Invalid network entity for season engraving network")
        return
    end
    
    if inst.prefab ~= "forest_network" and not inst.Network then
        print("[SeasonWorkshop] Warning: Season engraving network not on forest_network")
    end
    
    -- 网络变量映射表：为每个玩家创建网络变量
    self.player_net_vars = {}
    
    -- 使用严格的命名规范避免冲突
    local unique_prefix = "seasonworkshop_engraving_net_" .. tostring(inst.GUID) .. "_"
    
    -- 安全创建网络变量的辅助函数
    local function safe_create_netvar(var_type, name, event_name)
        local netvar = nil
        local success = pcall(function()
            if var_type == "string" then
                netvar = net_string(inst.GUID, name, event_name)
            end
        end)

        if not success or not netvar then
            print("[SeasonWorkshop] Error: Failed to create " .. var_type .. " network variable: " .. name)
            return nil
        end

        return netvar
    end
    
    -- 为最多8个玩家预创建网络变量（DST最大玩家数）
    for i = 1, 8 do
        local player_prefix = unique_prefix .. "p" .. i .. "_"
        
        local current_var = safe_create_netvar("string", player_prefix .. "current", "player_" .. i .. "_season_dirty")
        local manual_var = safe_create_netvar("string", player_prefix .. "manual", "player_" .. i .. "_manual_dirty")
        
        if current_var and manual_var then
            self.player_net_vars[i] = {
                current = current_var,
                manual = manual_var,
                userid = nil -- 将在玩家加入时设置
            }
        else
            print("[SeasonWorkshop] Warning: Failed to create network variables for player slot " .. i)
        end
    end
    
    self._network_initialized = true
    
    if TheWorld.ismastersim then
        -- 服务端：初始化网络变量值
        for i = 1, 8 do
            if self.player_net_vars[i] then
                local success1 = pcall(function() self.player_net_vars[i].current:set("") end)
                local success2 = pcall(function() self.player_net_vars[i].manual:set("") end)
                
                if not success1 or not success2 then
                    print("[SeasonWorkshop] Warning: Failed to initialize network variables for player slot " .. i)
                end
            end
        end
        
        -- 监听玩家加入和离开事件
        self.inst:ListenForEvent("ms_playerjoined", function(world, player)
            self:OnPlayerJoined(player)
        end, TheWorld)
        
        self.inst:ListenForEvent("ms_playerleft", function(world, player)
            self:OnPlayerLeft(player)
        end, TheWorld)
        
        -- 定期同步玩家季节状态
        self.sync_task = self.inst:DoPeriodicTask(1, function()
            self:SyncAllPlayerSeasons()
        end)
    else
        -- 客户端：监听网络同步事件
        for i = 1, 8 do
            if self.player_net_vars[i] then
                -- 监听当前季节变化
                self.inst:ListenForEvent("player_" .. i .. "_season_dirty", function()
                    self:OnPlayerSeasonChanged(i)
                end)
                
                -- 监听手动季节变化
                self.inst:ListenForEvent("player_" .. i .. "_manual_dirty", function()
                    self:OnPlayerManualSeasonChanged(i)
                end)
            end
        end
    end
end)

-- 玩家加入时分配网络变量槽位
function SeasonEngravingNetwork:OnPlayerJoined(player)
    if not TheWorld.ismastersim then return end
    
    local userid = player.userid
    if not userid then return end
    
    -- 查找空闲槽位
    for i = 1, 8 do
        if self.player_net_vars[i] and not self.player_net_vars[i].userid then
            self.player_net_vars[i].userid = userid
            print("[SeasonWorkshop] Assigned network slot " .. i .. " to player " .. userid)
            
            -- 立即同步该玩家的季节状态
            self:SyncPlayerSeason(player, i)
            break
        end
    end
end

-- 玩家离开时释放网络变量槽位
function SeasonEngravingNetwork:OnPlayerLeft(player)
    if not TheWorld.ismastersim then return end
    
    local userid = player.userid
    if not userid then return end
    
    -- 查找并释放槽位
    for i = 1, 8 do
        if self.player_net_vars[i] and self.player_net_vars[i].userid == userid then
            self.player_net_vars[i].userid = nil
            
            -- 清空网络变量
            local success1 = pcall(function() self.player_net_vars[i].current:set("") end)
            local success2 = pcall(function() self.player_net_vars[i].manual:set("") end)
            
            if success1 and success2 then
                print("[SeasonWorkshop] Released network slot " .. i .. " from player " .. userid)
            else
                print("[SeasonWorkshop] Warning: Failed to clear network slot " .. i)
            end
            break
        end
    end
end

-- 同步单个玩家的季节状态
function SeasonEngravingNetwork:SyncPlayerSeason(player, slot_index)
    if not TheWorld.ismastersim then return end
    if not player or not player:IsValid() then return end
    
    local engraving = player.components.season_engraving
    if not engraving then return end
    
    local slot = self.player_net_vars[slot_index]
    if not slot then return end
    
    -- 同步当前季节
    local current_season = engraving:GetSeason() or ""
    local manual_season = engraving:IsManualSeason() and current_season or ""
    
    local success1 = pcall(function() slot.current:set(current_season) end)
    local success2 = pcall(function() slot.manual:set(manual_season) end)
    
    if not success1 or not success2 then
        print("[SeasonWorkshop] Warning: Failed to sync season for player in slot " .. slot_index)
    end
end

-- 同步所有玩家的季节状态
function SeasonEngravingNetwork:SyncAllPlayerSeasons()
    if not TheWorld.ismastersim then return end
    
    for i = 1, 8 do
        local slot = self.player_net_vars[i]
        if slot and slot.userid then
            -- 查找对应的玩家
            local player = nil
            for _, p in ipairs(AllPlayers or {}) do
                if p.userid == slot.userid then
                    player = p
                    break
                end
            end
            
            if player then
                self:SyncPlayerSeason(player, i)
            end
        end
    end
end

-- 客户端：处理玩家季节变化
function SeasonEngravingNetwork:OnPlayerSeasonChanged(slot_index)
    if TheWorld.ismastersim then return end
    
    local slot = self.player_net_vars[slot_index]
    if not slot then return end
    
    local current_season = slot.current:value()
    if current_season and current_season ~= "" then
        -- 通知对应的玩家组件更新视觉效果
        self:NotifyPlayerSeasonChange(slot.userid, current_season, false)
    end
end

-- 客户端：处理玩家手动季节变化
function SeasonEngravingNetwork:OnPlayerManualSeasonChanged(slot_index)
    if TheWorld.ismastersim then return end
    
    local slot = self.player_net_vars[slot_index]
    if not slot then return end
    
    local manual_season = slot.manual:value()
    local is_manual = manual_season and manual_season ~= ""
    
    if is_manual then
        self:NotifyPlayerSeasonChange(slot.userid, manual_season, true)
    end
end

-- 通知玩家季节变化（客户端预测）
function SeasonEngravingNetwork:NotifyPlayerSeasonChange(userid, season, is_manual)
    if not userid then return end
    
    -- 查找对应的玩家
    local player = nil
    for _, p in ipairs(AllPlayers or {}) do
        if p.userid == userid then
            player = p
            break
        end
    end
    
    if player and player.components and player.components.season_engraving then
        -- 客户端预测：立即更新视觉效果
        player.components.season_engraving.current = season
        player.components.season_engraving.manual_season = is_manual and season or nil
        player.components.season_engraving:UpdateClientVisuals()
    end
end

-- 获取玩家的网络槽位
function SeasonEngravingNetwork:GetPlayerSlot(userid)
    for i = 1, 8 do
        if self.player_net_vars[i] and self.player_net_vars[i].userid == userid then
            return i
        end
    end
    return nil
end

-- 数据持久化
function SeasonEngravingNetwork:OnSave()
    local data = {
        player_assignments = {}
    }
    
    for i = 1, 8 do
        if self.player_net_vars[i] and self.player_net_vars[i].userid then
            data.player_assignments[i] = self.player_net_vars[i].userid
        end
    end
    
    return data
end

function SeasonEngravingNetwork:OnLoad(data)
    if not TheWorld.ismastersim then return end
    
    if data and data.player_assignments then
        for i, userid in pairs(data.player_assignments) do
            if self.player_net_vars[i] then
                self.player_net_vars[i].userid = userid
            end
        end
    end
end

-- 组件清理
function SeasonEngravingNetwork:OnRemoveFromEntity()
    if self.sync_task then
        self.sync_task:Cancel()
        self.sync_task = nil
    end
    
    -- 清理网络变量引用
    for i = 1, 8 do
        if self.player_net_vars[i] then
            self.player_net_vars[i].current = nil
            self.player_net_vars[i].manual = nil
            self.player_net_vars[i].userid = nil
        end
    end
    
    self.player_net_vars = {}
    print("[SeasonWorkshop] Season engraving network component cleaned up")
end

return SeasonEngravingNetwork
