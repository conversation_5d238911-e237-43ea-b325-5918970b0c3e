-- DST官方规范RPC系统实现
-- 基于DST的官方RPC机制，使用SendRPCToServer和AddClientRPC
-- 符合官方网络架构规范

local _G = GLOBAL
local TheWorld = _G.TheWorld
local TheNet = _G.TheNet
local AllPlayers = _G.AllPlayers
local SendRPCToServer = _G.SendRPCToServer
local AddClientRPC = _G.AddClientRPC

local RPCSystem = {}

-- RPC代码分配
RPCSystem.RPC_CODES = {
    SEASON_ENGRAVING_SET = nil,    -- 将在初始化时分配
    SEASON_ENGRAVING_CLEAR = nil,  -- 将在初始化时分配
    SEASON_ALTAR_SUMMON = nil,     -- 将在初始化时分配
}

-- RPC处理器注册表
RPCSystem.handlers = {}
RPCSystem.initialized = false
RPCSystem.next_rpc_code = 200 -- 从200开始，避免与游戏内置RPC冲突

-- 分配RPC代码
function RPCSystem.AllocateRPCCode(name)
    local code = RPCSystem.next_rpc_code
    RPCSystem.next_rpc_code = RPCSystem.next_rpc_code + 1
    RPCSystem.RPC_CODES[name] = code
    return code
end

-- 初始化RPC系统
function RPCSystem.Initialize()
    if RPCSystem.initialized then
        return true
    end

    if not TheWorld.ismastersim then
        print("[SeasonWorkshop] RPC system can only be initialized on server")
        return false
    end

    -- 分配RPC代码
    local season_set_code = RPCSystem.AllocateRPCCode("SEASON_ENGRAVING_SET")
    local season_clear_code = RPCSystem.AllocateRPCCode("SEASON_ENGRAVING_CLEAR")
    local altar_summon_code = RPCSystem.AllocateRPCCode("SEASON_ALTAR_SUMMON")

    -- 注册服务端RPC处理器
    AddClientRPC("SeasonWorkshop", "SetSeasonEngraving", function(player, season)
        if not player or not player:IsValid() then
            print("[SeasonWorkshop] RPC Error: Invalid player")
            return
        end

        if not player.components.season_engraving then
            print("[SeasonWorkshop] RPC Error: Player has no season engraving component")
            return
        end

        local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
        if not valid_seasons[season] then
            print("[SeasonWorkshop] RPC Error: Invalid season: " .. tostring(season))
            return
        end

        -- 执行季节设置
        local success = player.components.season_engraving:SetSeason(season)
        if success then
            print("[SeasonWorkshop] RPC: Player " .. (player.userid or "unknown") .. " set season to " .. season)
        else
            print("[SeasonWorkshop] RPC Error: Failed to set season for player")
        end
    end)

    AddClientRPC("SeasonWorkshop", "ClearSeasonEngraving", function(player)
        if not player or not player:IsValid() then
            print("[SeasonWorkshop] RPC Error: Invalid player")
            return
        end

        if not player.components.season_engraving then
            print("[SeasonWorkshop] RPC Error: Player has no season engraving component")
            return
        end

        -- 清除手动季节设置
        player.components.season_engraving:ClearManualSeason()
        print("[SeasonWorkshop] RPC: Player " .. (player.userid or "unknown") .. " cleared manual season")
    end)
    
    AddClientRPC("SeasonWorkshop", "SummonSeasonBoss", function(player, altar_guid)
        if not player or not player:IsValid() then
            print("[SeasonWorkshop] RPC Error: Invalid player")
            return
        end

        if not altar_guid then
            print("[SeasonWorkshop] RPC Error: No altar GUID provided")
            return
        end

        -- 查找祭坛实体
        local altar = nil
        for _, ent in pairs(_G.Ents or {}) do
            if ent.GUID == altar_guid and ent.prefab == "season_altar" then
                altar = ent
                break
            end
        end

        if not altar then
            print("[SeasonWorkshop] RPC Error: Altar not found")
            return
        end

        -- 检查玩家是否在祭坛附近
        local player_pos = player:GetPosition()
        local altar_pos = altar:GetPosition()
        local distance = player_pos:Dist(altar_pos)

        if distance > 5 then -- 5单位距离限制
            print("[SeasonWorkshop] RPC Error: Player too far from altar")
            return
        end

        -- 执行召唤逻辑（如果祭坛有相关组件）
        if altar.components and altar.components.season_altar then
            local success = altar.components.season_altar:TrySummon(player)
            if success then
                print("[SeasonWorkshop] RPC: Player " .. (player.userid or "unknown") .. " summoned boss via altar")
            else
                print("[SeasonWorkshop] RPC Error: Failed to summon boss")
            end
        else
            print("[SeasonWorkshop] RPC Error: Altar has no summon component")
        end
    end)
    
    RPCSystem.initialized = true
    print("[SeasonWorkshop] RPC system initialized with official DST RPC mechanism")
    return true
end

-- 客户端发送RPC（使用官方SendRPCToServer）
function RPCSystem.SendSeasonEngravingSet(season)
    if TheWorld.ismastersim then
        print("[SeasonWorkshop] Error: SendSeasonEngravingSet should only be called on client")
        return false
    end

    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    if not valid_seasons[season] then
        print("[SeasonWorkshop] Error: Invalid season: " .. tostring(season))
        return false
    end

    -- 使用官方RPC机制发送
    SendRPCToServer(RPC.SeasonWorkshop_SetSeasonEngraving, season)
    print("[SeasonWorkshop] Sent season engraving RPC: " .. season)
    return true
end

function RPCSystem.SendSeasonEngravingClear()
    if TheWorld.ismastersim then
        print("[SeasonWorkshop] Error: SendSeasonEngravingClear should only be called on client")
        return false
    end

    -- 使用官方RPC机制发送
    SendRPCToServer(RPC.SeasonWorkshop_ClearSeasonEngraving)
    print("[SeasonWorkshop] Sent clear season engraving RPC")
    return true
end

function RPCSystem.SendSeasonBossSummon(altar_guid)
    if TheWorld.ismastersim then
        print("[SeasonWorkshop] Error: SendSeasonBossSummon should only be called on client")
        return false
    end

    if not altar_guid then
        print("[SeasonWorkshop] Error: No altar GUID provided")
        return false
    end

    -- 使用官方RPC机制发送
    SendRPCToServer(RPC.SeasonWorkshop_SummonSeasonBoss, altar_guid)
    print("[SeasonWorkshop] Sent season boss summon RPC")
    return true
end

-- 获取RPC系统状态
function RPCSystem.GetStatus()
    return {
        initialized = RPCSystem.initialized,
        rpc_codes = RPCSystem.RPC_CODES
    }
end

-- 清理RPC系统
function RPCSystem.Cleanup()
    RPCSystem.initialized = false
    print("[SeasonWorkshop] RPC system cleaned up")
end



return RPCSystem
