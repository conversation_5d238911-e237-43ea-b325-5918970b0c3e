-- 季节刻印Classified实体
-- 根据DST官方规范实现的网络同步分类实体
-- 用于在服务端和客户端之间同步季节刻印数据

local _G = GLOBAL

local function OnEntityReplicated(inst)
    -- 客户端：实体复制完成后的初始化
    inst.replica.season_engraving_classified = inst.components.season_engraving_classified
end

local function fn()
    local inst = _G.CreateEntity()
    
    -- 基础实体组件
    inst.entity:AddTransform()
    inst.entity:AddNetwork()
    
    -- 标记为classified实体
    inst:AddTag("CLASSIFIED")
    inst:AddTag("season_engraving_classified")
    
    -- 添加classified组件
    inst:AddComponent("season_engraving_classified")
    
    -- 网络同步分界点
    inst.entity:SetPristine()
    
    -- 客户端初始化
    if not _G.TheWorld.ismastersim then
        -- 设置实体复制回调
        inst.OnEntityReplicated = OnEntityReplicated
        return inst
    end
    
    -- 服务端初始化
    inst:AddComponent("inspectable")
    
    -- 持久化支持
    inst.persists = false -- classified实体通常不需要持久化
    
    return inst
end

-- 创建classified实体的工厂函数
local function CreateSeasonEngravingClassified(player)
    if not player or not player:IsValid() then
        print("[SeasonWorkshop] Error: Invalid player for classified creation")
        return nil
    end
    
    local classified = _G.SpawnPrefab("season_engraving_classified")
    if not classified then
        print("[SeasonWorkshop] Error: Failed to spawn season engraving classified")
        return nil
    end
    
    -- 设置关联的玩家
    if classified.components and classified.components.season_engraving_classified then
        classified.components.season_engraving_classified:SetPlayer(player)
    end
    
    -- 将classified附加到玩家
    if player.season_engraving_classified then
        -- 移除旧的classified
        player.season_engraving_classified:Remove()
    end
    
    player.season_engraving_classified = classified
    
    -- 设置classified的位置（通常跟随玩家）
    classified.Transform:SetPosition(player.Transform:GetWorldPosition())
    
    print(string.format("[SeasonWorkshop] Created season engraving classified for player %s", 
        player.prefab or "unknown"))
    
    return classified
end

-- 清理玩家的classified实体
local function CleanupSeasonEngravingClassified(player)
    if player and player.season_engraving_classified then
        if player.season_engraving_classified:IsValid() then
            player.season_engraving_classified:Remove()
        end
        player.season_engraving_classified = nil
        
        print(string.format("[SeasonWorkshop] Cleaned up season engraving classified for player %s", 
            player.prefab or "unknown"))
    end
end

-- 获取或创建玩家的classified实体
local function GetOrCreateSeasonEngravingClassified(player)
    if not player or not player:IsValid() then
        return nil
    end
    
    -- 检查现有的classified
    if player.season_engraving_classified and player.season_engraving_classified:IsValid() then
        return player.season_engraving_classified
    end
    
    -- 创建新的classified
    return CreateSeasonEngravingClassified(player)
end

-- 验证classified实体状态
local function ValidateSeasonEngravingClassified(player)
    if not player or not player:IsValid() then
        return false, "Invalid player"
    end
    
    local classified = player.season_engraving_classified
    if not classified or not classified:IsValid() then
        return false, "Missing or invalid classified entity"
    end
    
    if not classified.components or not classified.components.season_engraving_classified then
        return false, "Missing classified component"
    end
    
    -- 验证网络同步状态
    local sync_valid, sync_issues = classified.components.season_engraving_classified:ValidateNetworkSync()
    if not sync_valid then
        return false, "Network sync issues: " .. table.concat(sync_issues, ", ")
    end
    
    return true, "Valid"
end

-- 同步所有玩家的classified数据
local function SyncAllSeasonEngravingClassified()
    if not _G.TheWorld.ismastersim then return end
    
    local synced_count = 0
    local failed_count = 0
    
    for _, player in ipairs(_G.AllPlayers or {}) do
        if player and player:IsValid() and player.season_engraving_classified then
            local classified = player.season_engraving_classified
            if classified:IsValid() and classified.components and classified.components.season_engraving_classified then
                classified.components.season_engraving_classified:SyncToClient()
                synced_count = synced_count + 1
            else
                failed_count = failed_count + 1
            end
        end
    end
    
    print(string.format("[SeasonWorkshop] Synced %d classified entities, %d failed", 
        synced_count, failed_count))
    
    return synced_count, failed_count
end

-- 导出函数
_G.CreateSeasonEngravingClassified = CreateSeasonEngravingClassified
_G.CleanupSeasonEngravingClassified = CleanupSeasonEngravingClassified
_G.GetOrCreateSeasonEngravingClassified = GetOrCreateSeasonEngravingClassified
_G.ValidateSeasonEngravingClassified = ValidateSeasonEngravingClassified
_G.SyncAllSeasonEngravingClassified = SyncAllSeasonEngravingClassified

return _G.Prefab("season_engraving_classified", fn, {})
