[00:00:00]: 
System Memory:
	Memory Load: 92%
	Available Physical Memory: 1123m/14204m
	Available Page File: 17423m/38780m
	Available Virtual Memory: 134213452m/134217727m
	Available Extended Virtual Memory: 0m
[00:00:00]: 
Process Memory:
	Peak Working Set Size: 36m
	Working Set Size: 36m
	Quota Peak Page Pool Usage: 260k
	Quota Page Pool Usage: 258k
	Quota Peak Non Paged Pool Usage:18k
	Quota Non Paged Pool Usage: 18k
	Page File Usage: 6m
	Peak Page File Usage: 6m
[00:00:00]: PersistRootStorage is now APP:Klei//DoNotStarveTogether/ 
[00:00:00]: Starting Up
[00:00:00]: Version: 682257
[00:00:00]: Current time: Thu Aug 14 20:47:30 2025

[00:00:00]: Don't Starve Together: 682257 WIN32_STEAM
[00:00:00]: Build Date: 3490
[00:00:00]: Mode: 64-bit
[00:00:00]: Parsing command line
[00:00:00]: Command Line Arguments: 
[00:00:00]: Initializing distribution platform
[00:00:00]: Initializing Minidump handler
[00:00:00]: ....Done
[00:00:01]: Steam AppBuildID: 19504765
[00:00:01]: ....Done
[00:00:01]: PersistUserStorage is now APP:Klei//DoNotStarveTogether/*********/ 
[00:00:01]: Fixing DPI
[00:00:01]: ...Done
[00:00:01]: THREAD - started 'GAClient' (45648)
[00:00:01]: CurlRequestManager::ClientThread::Main()
[00:00:01]: ProfileIndex:0.81
[00:00:01]: Mounting file system databundles/klump.zip successful.
[00:00:01]: Mounting file system databundles/shaders.zip successful.
[00:00:01]: Mounting file system databundles/fonts.zip successful.
[00:00:01]: Mounting file system databundles/anim_dynamic.zip successful.
[00:00:01]: Mounting file system databundles/bigportraits.zip successful.
[00:00:01]: Mounting file system databundles/images.zip successful.
[00:00:01]: Mounting file system databundles/scripts.zip successful.
[00:00:01]: THREAD - started 'IC' (62280)
[00:00:01]: Texture Streaming: ENABLED
[00:00:01]: Small Textures: DISABLED
[00:00:01]: Threaded Renderer: ENABLED
[00:00:01]: THREAD - started 'SimUpdateThread' (80116)
[00:00:01]: THREAD - started 'RenderThread' (123180)
[00:00:01]: [Connect] PendingConnection::Reset(true)
[00:00:01]: Platform: 1
[00:00:01]: Network tick rate: U=15(2), D=0
[00:00:01]: Authorized application E:\SteamLibrary\steamapps\common\Don't Starve Together\bin64\dontstarve_steam_x64.exe is enabled in the firewall.
[00:00:01]: WindowsFirewall - Application already authorized
[00:00:01]: THREAD - started 'StreamInput' (52812)
[00:00:01]: loaded ping_cache
[00:00:01]: Offline user ID: OU_76561198271831083
[00:00:01]: Cached userid loaded:
[00:00:01]: SteamID: 76561198271831083
[00:00:01]: THREAD - started 'AsynTextureLoadthread' (14536)
[00:00:01]: THREAD - started 'AsynTextureLoadthread' (6672)
[00:00:01]: HardwareStats:
  OS                        
    name                      Microsoft Windows 11 专业版
    version                   10.0.26100
    platformSpecific          SP 0.0
    architecture              64 位
  CPU                       
    clockSpeed                3301
    name                      AMD Ryzen 5 5600H with Radeon Graphics         
    manufacturer              AuthenticAMD
    numCores                  6
    features                  SSE,SSE2,SSE3,SSSE3,SSE41,SSE42,AVX
  RAM                       
    megsOfRam                 16384
  GPU                       
    megsOfRam                 4095
    videoModeDescription      1920 x 1080 x 4294967296 种颜色
    name                      NVIDIA GeForce RTX 3050 Laptop GPU
    driverVersion             32.0.15.6614
    driverDate                20241106000000.000000-000
    refreshRate               165

[00:00:01]: cGame::InitializeOnMainThread
[00:00:01]: WindowManager::Initialize
[00:00:01]: CreateWindow: Requesting 1920,1080 - 5/6/5 - -1/-1/-1 - 0
[00:00:01]: CreateEGLContext: 12 configs found
[00:00:01]:      0: 8/8/8 -  0/ 0/ 0 - 0
[00:00:01]:      1: 8/8/8 -  0/16/ 0 - 0
[00:00:01]:      2: 8/8/8 -  0/24/ 0 - 0
[00:00:01]:      3: 8/8/8 -  0/24/ 8 - 0
[00:00:01]:      4: 5/5/5 -  0/ 0/ 0 - 0
[00:00:01]:      5: 5/5/5 -  0/16/ 0 - 0
[00:00:01]:      6: 5/5/5 -  0/24/ 0 - 0
[00:00:01]:      7: 5/5/5 -  0/24/ 8 - 0
[00:00:01]:      8: 8/8/8 -  8/ 0/ 0 - 0
[00:00:01]:      9: 8/8/8 -  8/16/ 0 - 0
[00:00:01]:     10: 8/8/8 -  8/24/ 0 - 0
[00:00:01]:     11: 8/8/8 -  8/24/ 8 - 0
[00:00:02]: WindowManager::SetFullscreen(0, 1920, 1080, 60)
[00:00:03]: GLInfo
[00:00:03]: ~~~~~~
[00:00:03]: GL_VENDOR: Google Inc.
[00:00:03]: GL_RENDERER: ANGLE (NVIDIA GeForce RTX 3050 Laptop GPU)
[00:00:03]: GL_VERSION: OpenGL ES 2.0 (ANGLE 1.0.0.2249)
[00:00:03]: GL_SHADING_LANGUAGE_VERSION: OpenGL ES GLSL ES 1.00 (ANGLE 1.0.0.2249)
[00:00:03]: THREAD - started 'WindowsInputManager' (121828)
[00:00:03]: OpenGL extensions (21, 21):
[00:00:03]: GL_ANGLE_depth_texture
[00:00:03]: GL_ANGLE_framebuffer_blit
[00:00:03]: GL_ANGLE_framebuffer_multisample
[00:00:03]: GL_ANGLE_instanced_arrays
[00:00:03]: GL_ANGLE_pack_reverse_row_order
[00:00:03]: GL_ANGLE_texture_compression_dxt3
[00:00:03]: GL_ANGLE_texture_compression_dxt5
[00:00:03]: GL_ANGLE_texture_usage
[00:00:03]: GL_ANGLE_translated_shader_source
[00:00:03]: GL_EXT_read_format_bgra
[00:00:03]: GL_EXT_robustness
[00:00:03]: GL_EXT_texture_compression_dxt1
[00:00:03]: GL_EXT_texture_format_BGRA8888
[00:00:03]: GL_EXT_texture_storage
[00:00:03]: GL_OES_get_program_binary
[00:00:03]: GL_OES_packed_depth_stencil
[00:00:03]: GL_OES_rgb8_rgba8
[00:00:03]: GL_OES_standard_derivatives
[00:00:03]: GL_OES_texture_float_linear
[00:00:03]: GL_OES_texture_half_float_linear
[00:00:03]: GL_OES_texture_npot
[00:00:03]: GL_MAX_TEXTURE_SIZE = 16384
[00:00:03]: GL_MAX_TEXTURE_IMAGE_UNITS = 16
[00:00:03]: GL_MAX_RENDERBUFFER_SIZE = 16384
[00:00:03]: GL_MAX_VIEWPORT_DIMS = 16384, 16384
[00:00:03]: GL_MAX_VARYING_VECTORS = 10
[00:00:03]: GL_MAX_VERTEX_ATTRIBS = 16
[00:00:03]: GL_MAX_VERTEX_UNIFORM_VECTORS = 254
[00:00:03]: GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS = 4
[00:00:03]: GL_MAX_FRAGMENT_UNIFORM_VECTORS = 221
[00:00:03]: 4 compressed texture formats
[00:00:03]: texture format 0x83f0
[00:00:03]: texture format 0x83f1
[00:00:03]: texture format 0x83f2
[00:00:03]: texture format 0x83f3
[00:00:03]: Renderer initialize: Okay
[00:00:03]: AnimManager initialize: Okay
[00:00:04]: Buffers initialize: Okay
[00:00:04]: cDontStarveGame::DoGameSpecificInitialize()
[00:00:04]: GameSpecific initialize: Okay
[00:00:04]: cGame::StartPlaying
[00:00:04]: AppVersion::GetArchitecture() x64
[00:00:04]: LOADING LUA
[00:00:04]: DoLuaFile scripts/main.lua
[00:00:04]: DoLuaFile loading buffer scripts/main.lua
[00:00:04]: Translator:LoadPOFile - loading file: scripts/languages/chinese_s.po	
[00:00:07]:   taskgrouplist:	default	联机版	
[00:00:07]:   taskgrouplist:	classic	经典	
[00:00:07]:   taskgrouplist:	cave_default	地下	
[00:00:07]:   taskgrouplist:	lavaarena_taskset	熔炉	
[00:00:07]:   taskgrouplist:	quagmire_taskset	暴食	
[00:00:07]: Running main.lua
	
[00:00:07]: loaded modindex	
[00:00:07]: ModIndex:GetModsToLoad inserting moddir, 	thinking	
[00:00:07]: ModIndex: Detected bad load, disabling all mods.	
[00:00:07]: Event data unavailable: lavaarena_event_server/lavaarena_achievement_quest_defs
[00:00:11]: LOADING LUA SUCCESS
[00:00:11]: PlayerDeaths loaded morgue	566	
[00:00:11]: PlayerHistory loaded player_history (v2) len:671	
[00:00:11]: ServerPreferences could not load server_preferences	
[00:00:11]: ConsoleScreenSettings could not load consolescreen	
[00:00:11]: bloom_enabled	true	
[00:00:11]: EnableShadeRenderer: 	true	
[00:00:11]: loaded shardsaveindex	
[00:00:11]: OnFilesLoaded()	
[00:00:11]: OnUpdatePurchaseStateComplete	
[00:00:11]: Klump load on boot started.	
[00:00:11]: Klump files loaded: 	0	
[00:00:14]: 	Load FE	
[00:00:14]: 	Load FE: done	
[00:00:14]: THREAD - started 'FilesExistAsyncThread' (124600)
[00:00:14]: FilesExistAsyncThread started (27242 files)...
[00:00:14]: OnLoadPermissionList: APP:Klei//DoNotStarveTogether/*********/client_save/blocklist.txt (Failure)
[00:00:14]: OnLoadPermissionList: APP:Klei//DoNotStarveTogether/*********/client_save/adminlist.txt (Failure)
[00:00:14]: OnLoadUserIdList: APP:Klei//DoNotStarveTogether/*********/client_save/whitelist.txt (Failure)
[00:00:14]: [MOTD] Downloading info from	https://motd.klei.com/motd.json/?game=dst&platform=STEAM&locale=CN&lang=schinese&version=682257	
[00:00:15]: Check for write access: TRUE
[00:00:15]: Check for read access: TRUE
[00:00:15]: Available disk space for save files: 38657 MB
[00:00:15]: ModIndex: Load sequence finished successfully.	
[00:00:15]: Reset() returning
[00:00:15]: [MOTD] Done Loading.	
[00:00:17]: ... FilesExistAsyncThread complete
[00:00:34]: Do AutoLogin	
[00:00:34]: [Steam] Auth Session Ticket requested...
[00:00:35]: [Steam] Got Auth Session Ticket
[00:00:39]: [200] Account Communication Success (3)
[00:00:39]: Logging in as KU_cuIT1jJp
[00:00:40]: There is no active event to get the status of.
[00:00:40]: [DLCS] 1, 2132800:0, 1338540:0, 1485060:0, 2236180:0, 282470:1:0, 840750:0, 1558300:0, 1205500:0, 1084430:0, 1289760:0, 1899050:0, 1051700:0, 3155070:0, 1205501:0, 981701:0, 840770:0, 1167510:0, 1447360:0, 981700:0, 840760:0, 382560:0, 1694780:0, 1874370:0, 798750:0, 1522880:0, 1230830:0, 1710241:0, 457140:1:0, 1172860:0, 1272440:1:0, 1710240:1:0, 2241030:1:0, 2711510:0, 3373370:0, 393010:1:0, 987340:0, 2058281:0, 962960:0, 1167330:0, 2170670:0, 2058280:0, 798740:0, 2170671:0, 1612410:0, 1836630:0, 2769680:0, 3373360:0, 974740:0, 1698560:0, 2377640:0, 2007370:0, 712640:1:0, 981702:0
[00:00:44]: could not load pending_keyvalues_prod
[00:00:45]: loaded inventory_cache_prod
[00:00:45]: using inventory state token from cached file
[00:00:47]: saved APP:Klei//DoNotStarveTogether/*********/client_save/inventory_cache_prod
[00:00:47]: saved APP:Klei//DoNotStarveTogether/*********/client_save/inventory_cache_prod_sig
[00:00:47]: [Workshop] ItemQuery got this many results: 35, matching: 35
[00:00:47]: [Workshop] ModQuery got 35 results
[00:00:47]: [Workshop] ModQuery skipping bad result: 9, ID: 610528767
[00:00:47]: [Workshop] ModQuery queued to download IDs: 2753774601 2744733876 2678400698 2606087644 2505341606 2303894106 2189004162 2078243581 1909182187 1608191708 1595631294 1530801499 1425714659 1392778117 1365141672 1265737101 1231473531 1207269058 1120124958 956206484 836583293 822508420 727774324 694531911 666155465 661253977 623749604 572538624 378160973 376333686 375850593 362175979 352373173 351325790
[00:00:47]: [Workshop] ItemQuery finished all queries, moving to download phase
[00:00:47]: [Workshop] ItemQuery finished downloading everything
[00:01:27]: Getting top mod details...
[00:01:27]: Frontend-Unloading mod 'all'.	
[00:01:27]: GOT top mod details...50
[00:01:27]: [Workshop] ItemQuery got this many results: 35, matching: 35
[00:01:27]: [Workshop] ModQuery got 35 results
[00:01:27]: [Workshop] ModQuery skipping bad result: 9, ID: 610528767
[00:01:27]: [Workshop] ModQuery queued to download IDs: 2753774601 2744733876 2678400698 2606087644 2505341606 2303894106 2189004162 2078243581 1909182187 1608191708 1595631294 1530801499 1425714659 1392778117 1365141672 1265737101 1231473531 1207269058 1120124958 956206484 836583293 822508420 727774324 694531911 666155465 661253977 623749604 572538624 378160973 376333686 375850593 362175979 352373173 351325790
[00:01:27]: [Workshop] ItemQuery finished all queries, moving to download phase
[00:01:27]: [Workshop] ItemQuery finished downloading everything
[00:01:27]: [Workshop] ItemQuery got this many results: 35, matching: 35
[00:01:27]: [Workshop] ModQuery got 35 results
[00:01:27]: [Workshop] ModQuery skipping bad result: 9, ID: 610528767
[00:01:27]: [Workshop] ModQuery queued to download IDs: 2753774601 2744733876 2678400698 2606087644 2505341606 2303894106 2189004162 2078243581 1909182187 1608191708 1595631294 1530801499 1425714659 1392778117 1365141672 1265737101 1231473531 1207269058 1120124958 956206484 836583293 822508420 727774324 694531911 666155465 661253977 623749604 572538624 378160973 376333686 375850593 362175979 352373173 351325790
[00:01:27]: [Workshop] ItemQuery finished all queries, moving to download phase
[00:01:27]: [Workshop] ItemQuery finished downloading everything
[00:01:31]: FrontendLoadMod	thinking	
[00:01:31]: Could not load mod_config_data/modconfiguration_thinking	
[00:01:31]: Fontend-Loading mod: thinking (Season Workshop (四季工坊)) Version:0.1.0	
[00:01:31]: Mod: thinking (Season Workshop (四季工坊))	Loading modservercreationmain.lua	
[00:01:31]: Mod: thinking (Season Workshop (四季工坊))	  Mod had no modservercreationmain.lua. Skipping.	
[00:01:31]: Mod: thinking (Season Workshop (四季工坊))	Loading modworldgenmain.lua	
[00:01:31]: Mod: thinking (Season Workshop (四季工坊))	  Mod had no modworldgenmain.lua. Skipping.	
[00:01:33]: [Workshop] ItemQuery got this many results: 35, matching: 35
[00:01:33]: [Workshop] ModQuery got 35 results
[00:01:33]: [Workshop] ModQuery skipping bad result: 9, ID: 610528767
[00:01:33]: [Workshop] ModQuery queued to download IDs: 2753774601 2744733876 2678400698 2606087644 2505341606 2303894106 2189004162 2078243581 1909182187 1608191708 1595631294 1530801499 1425714659 1392778117 1365141672 1265737101 1231473531 1207269058 1120124958 956206484 836583293 822508420 727774324 694531911 666155465 661253977 623749604 572538624 378160973 376333686 375850593 362175979 352373173 351325790
[00:01:33]: [Workshop] ItemQuery finished all queries, moving to download phase
[00:01:33]: [Workshop] ItemQuery finished downloading everything
[00:01:33]: [Workshop] ItemQuery got this many results: 35, matching: 35
[00:01:33]: [Workshop] ModQuery got 35 results
[00:01:33]: [Workshop] ModQuery skipping bad result: 9, ID: 610528767
[00:01:33]: [Workshop] ModQuery queued to download IDs: 2753774601 2744733876 2678400698 2606087644 2505341606 2303894106 2189004162 2078243581 1909182187 1608191708 1595631294 1530801499 1425714659 1392778117 1365141672 1265737101 1231473531 1207269058 1120124958 956206484 836583293 822508420 727774324 694531911 666155465 661253977 623749604 572538624 378160973 376333686 375850593 362175979 352373173 351325790
[00:01:33]: [Workshop] ItemQuery finished all queries, moving to download phase
[00:01:33]: [Workshop] ItemQuery finished downloading everything
[00:01:34]: Could not load mod_config_data/modconfiguration_thinking	
[00:01:34]: Network tick rate: U=15(2), D=0
[00:01:34]: [Workshop] CancelDownloads for all pending downloads
[00:01:34]: About to start a server with the following settings:
[00:01:34]:   Dedicated: false
[00:01:34]:   Online: true
[00:01:34]:   Passworded: false
[00:01:34]:   ServerPort: 10999
[00:01:34]:   SteamAuthPort: 8766
[00:01:34]:   SteamMasterServerPort: 27016
[00:01:34]:   ClanID: false
[00:01:34]:   ClanOnly: false
[00:01:34]:   ClanAdmin: false
[00:01:34]:   LanOnly: false
[00:01:34]:   FriendsOnly: true
[00:01:34]:   EnableAutosaver: true
[00:01:34]:   EncodeUserPath: true
[00:01:34]:   PVP: false
[00:01:34]:   MaxPlayers: 6
[00:01:34]:   GameMode: survival
[00:01:34]:   OverridenDNS: 
[00:01:34]:   PauseWhenEmpty: true
[00:01:34]:   IdleTimeout: 1800s
[00:01:34]:   VoteEnabled: false
[00:01:34]:   InternetBroadcasting: true
[00:01:34]: [Warning] Could not confirm port 10999 is open in the firewall. 
[00:01:34]: Could not load mod_config_data/modconfiguration_thinking	
[00:01:34]: Online Server Started on port: 10999
[00:01:35]: SUCCESS: Loaded modoverrides.lua	
[00:01:35]: Found a level data override file with these contents:	
[00:01:35]: 	K: 	desc	 V: 	更轻松的游戏方式，更少受到来自世界的威胁。
饥饿、寒冷、过热和黑暗将不会杀死冒险家。
降低冒险家受到的伤害。永远可以在绚丽之门复活。	
[00:01:35]: 	K: 	hideminimap	 V: 	false	
[00:01:35]: 	K: 	id	 V: 	RELAXED	
[00:01:35]: 	K: 	location	 V: 	forest	
[00:01:35]: 	K: 	max_playlist_position	 V: 	999	
[00:01:35]: 	K: 	min_playlist_position	 V: 	0	
[00:01:35]: 	K: 	name	 V: 	轻松	
[00:01:35]: 	K: 	numrandom_set_pieces	 V: 	4	
[00:01:35]: 	K: 	override_level_string	 V: 	false	
[00:01:35]: 	K: 	overrides	 V: 	table: 00000000154348A0	
[00:01:35]: 		K: 	alternatehunt	 V: 	default	
[00:01:35]: 		K: 	angrybees	 V: 	default	
[00:01:35]: 		K: 	antliontribute	 V: 	default	
[00:01:35]: 		K: 	autumn	 V: 	default	
[00:01:35]: 		K: 	balatro	 V: 	default	
[00:01:35]: 		K: 	bananabush_portalrate	 V: 	default	
[00:01:35]: 		K: 	basicresource_regrowth	 V: 	none	
[00:01:35]: 		K: 	bats_setting	 V: 	default	
[00:01:35]: 		K: 	bearger	 V: 	default	
[00:01:35]: 		K: 	beefalo	 V: 	default	
[00:01:35]: 		K: 	beefaloheat	 V: 	default	
[00:01:35]: 		K: 	beequeen	 V: 	default	
[00:01:35]: 		K: 	bees	 V: 	default	
[00:01:35]: 		K: 	bees_setting	 V: 	default	
[00:01:35]: 		K: 	berrybush	 V: 	default	
[00:01:35]: 		K: 	birds	 V: 	default	
[00:01:35]: 		K: 	boons	 V: 	default	
[00:01:35]: 		K: 	branching	 V: 	default	
[00:01:35]: 		K: 	brightmarecreatures	 V: 	rare	
[00:01:35]: 		K: 	bunnymen_setting	 V: 	default	
[00:01:35]: 		K: 	butterfly	 V: 	default	
[00:01:35]: 		K: 	buzzard	 V: 	default	
[00:01:35]: 		K: 	cactus	 V: 	default	
[00:01:35]: 		K: 	cactus_regrowth	 V: 	default	
[00:01:35]: 		K: 	carrot	 V: 	default	
[00:01:35]: 		K: 	carrots_regrowth	 V: 	default	
[00:01:35]: 		K: 	catcoon	 V: 	default	
[00:01:35]: 		K: 	catcoons	 V: 	default	
[00:01:35]: 		K: 	chess	 V: 	default	
[00:01:35]: 		K: 	cookiecutters	 V: 	default	
[00:01:35]: 		K: 	crabking	 V: 	default	
[00:01:35]: 		K: 	crow_carnival	 V: 	default	
[00:01:35]: 		K: 	darkness	 V: 	nonlethal	
[00:01:35]: 		K: 	day	 V: 	default	
[00:01:35]: 		K: 	daywalker2	 V: 	default	
[00:01:35]: 		K: 	deciduousmonster	 V: 	default	
[00:01:35]: 		K: 	deciduoustree_regrowth	 V: 	default	
[00:01:35]: 		K: 	deerclops	 V: 	default	
[00:01:35]: 		K: 	dragonfly	 V: 	default	
[00:01:35]: 		K: 	dropeverythingondespawn	 V: 	default	
[00:01:35]: 		K: 	evergreen_regrowth	 V: 	default	
[00:01:35]: 		K: 	extrastartingitems	 V: 	default	
[00:01:35]: 		K: 	eyeofterror	 V: 	default	
[00:01:35]: 		K: 	fishschools	 V: 	default	
[00:01:35]: 		K: 	flint	 V: 	default	
[00:01:35]: 		K: 	flowers	 V: 	default	
[00:01:35]: 		K: 	flowers_regrowth	 V: 	default	
[00:01:35]: 		K: 	frograin	 V: 	default	
[00:01:35]: 		K: 	frogs	 V: 	default	
[00:01:35]: 		K: 	fruitfly	 V: 	default	
[00:01:35]: 		K: 	ghostenabled	 V: 	always	
[00:01:35]: 		K: 	ghostsanitydrain	 V: 	none	
[00:01:35]: 		K: 	gnarwail	 V: 	default	
[00:01:35]: 		K: 	goosemoose	 V: 	default	
[00:01:35]: 		K: 	grass	 V: 	default	
[00:01:35]: 		K: 	grassgekkos	 V: 	default	
[00:01:35]: 		K: 	hallowed_nights	 V: 	default	
[00:01:35]: 		K: 	has_ocean	 V: 	true	
[00:01:35]: 		K: 	healthpenalty	 V: 	none	
[00:01:35]: 		K: 	hound_mounds	 V: 	default	
[00:01:35]: 		K: 	houndmound	 V: 	default	
[00:01:35]: 		K: 	hounds	 V: 	rare	
[00:01:35]: 		K: 	hunger	 V: 	nonlethal	
[00:01:35]: 		K: 	hunt	 V: 	default	
[00:01:35]: 		K: 	junkyard	 V: 	default	
[00:01:35]: 		K: 	keep_disconnected_tiles	 V: 	true	
[00:01:35]: 		K: 	klaus	 V: 	default	
[00:01:35]: 		K: 	krampus	 V: 	default	
[00:01:35]: 		K: 	layout_mode	 V: 	LinkNodesByKeys	
[00:01:35]: 		K: 	lessdamagetaken	 V: 	always	
[00:01:35]: 		K: 	liefs	 V: 	default	
[00:01:35]: 		K: 	lightcrab_portalrate	 V: 	default	
[00:01:35]: 		K: 	lightning	 V: 	default	
[00:01:35]: 		K: 	lightninggoat	 V: 	default	
[00:01:35]: 		K: 	loop	 V: 	default	
[00:01:35]: 		K: 	lunarhail_frequency	 V: 	default	
[00:01:35]: 		K: 	lureplants	 V: 	default	
[00:01:35]: 		K: 	malbatross	 V: 	default	
[00:01:35]: 		K: 	marshbush	 V: 	default	
[00:01:35]: 		K: 	merm	 V: 	default	
[00:01:35]: 		K: 	merms	 V: 	default	
[00:01:35]: 		K: 	meteorshowers	 V: 	default	
[00:01:35]: 		K: 	meteorspawner	 V: 	default	
[00:01:35]: 		K: 	moles	 V: 	default	
[00:01:35]: 		K: 	moles_setting	 V: 	default	
[00:01:35]: 		K: 	monkeytail_portalrate	 V: 	default	
[00:01:35]: 		K: 	moon_berrybush	 V: 	default	
[00:01:35]: 		K: 	moon_bullkelp	 V: 	default	
[00:01:35]: 		K: 	moon_carrot	 V: 	default	
[00:01:35]: 		K: 	moon_fissure	 V: 	default	
[00:01:35]: 		K: 	moon_fruitdragon	 V: 	default	
[00:01:35]: 		K: 	moon_hotspring	 V: 	default	
[00:01:35]: 		K: 	moon_rock	 V: 	default	
[00:01:35]: 		K: 	moon_sapling	 V: 	default	
[00:01:35]: 		K: 	moon_spider	 V: 	default	
[00:01:35]: 		K: 	moon_spiders	 V: 	default	
[00:01:35]: 		K: 	moon_starfish	 V: 	default	
[00:01:35]: 		K: 	moon_tree	 V: 	default	
[00:01:35]: 		K: 	moon_tree_regrowth	 V: 	default	
[00:01:35]: 		K: 	mosquitos	 V: 	default	
[00:01:35]: 		K: 	mushroom	 V: 	default	
[00:01:35]: 		K: 	mutated_hounds	 V: 	default	
[00:01:35]: 		K: 	no_joining_islands	 V: 	true	
[00:01:35]: 		K: 	no_wormholes_to_disconnected_tiles	 V: 	true	
[00:01:35]: 		K: 	ocean_bullkelp	 V: 	default	
[00:01:35]: 		K: 	ocean_otterdens	 V: 	default	
[00:01:35]: 		K: 	ocean_seastack	 V: 	ocean_default	
[00:01:35]: 		K: 	ocean_shoal	 V: 	default	
[00:01:35]: 		K: 	ocean_waterplant	 V: 	ocean_default	
[00:01:35]: 		K: 	ocean_wobsterden	 V: 	default	
[00:01:35]: 		K: 	otters_setting	 V: 	default	
[00:01:35]: 		K: 	palmcone_seed_portalrate	 V: 	default	
[00:01:35]: 		K: 	palmconetree	 V: 	default	
[00:01:35]: 		K: 	palmconetree_regrowth	 V: 	default	
[00:01:35]: 		K: 	penguins	 V: 	default	
[00:01:35]: 		K: 	penguins_moon	 V: 	default	
[00:01:35]: 		K: 	perd	 V: 	default	
[00:01:35]: 		K: 	petrification	 V: 	default	
[00:01:35]: 		K: 	pigs	 V: 	default	
[00:01:35]: 		K: 	pigs_setting	 V: 	default	
[00:01:35]: 		K: 	pirateraids	 V: 	default	
[00:01:35]: 		K: 	ponds	 V: 	default	
[00:01:35]: 		K: 	portal_spawnrate	 V: 	default	
[00:01:35]: 		K: 	portalresurection	 V: 	always	
[00:01:35]: 		K: 	powder_monkey_portalrate	 V: 	default	
[00:01:35]: 		K: 	prefabswaps_start	 V: 	default	
[00:01:35]: 		K: 	rabbits	 V: 	default	
[00:01:35]: 		K: 	rabbits_setting	 V: 	default	
[00:01:35]: 		K: 	reeds	 V: 	default	
[00:01:35]: 		K: 	reeds_regrowth	 V: 	default	
[00:01:35]: 		K: 	regrowth	 V: 	default	
[00:01:35]: 		K: 	resettime	 V: 	none	
[00:01:35]: 		K: 	rifts_enabled	 V: 	default	
[00:01:35]: 		K: 	rifts_frequency	 V: 	default	
[00:01:35]: 		K: 	roads	 V: 	default	
[00:01:35]: 		K: 	rock	 V: 	default	
[00:01:35]: 		K: 	rock_ice	 V: 	default	
[00:01:35]: 		K: 	saltstack_regrowth	 V: 	default	
[00:01:35]: 		K: 	sapling	 V: 	default	
[00:01:35]: 		K: 	season_start	 V: 	default	
[00:01:35]: 		K: 	seasonalstartingitems	 V: 	default	
[00:01:35]: 		K: 	shadowcreatures	 V: 	rare	
[00:01:35]: 		K: 	sharkboi	 V: 	default	
[00:01:35]: 		K: 	sharks	 V: 	default	
[00:01:35]: 		K: 	spawnmode	 V: 	fixed	
[00:01:35]: 		K: 	spawnprotection	 V: 	default	
[00:01:35]: 		K: 	specialevent	 V: 	default	
[00:01:35]: 		K: 	spider_warriors	 V: 	default	
[00:01:35]: 		K: 	spiderqueen	 V: 	default	
[00:01:35]: 		K: 	spiders	 V: 	default	
[00:01:35]: 		K: 	spiders_setting	 V: 	default	
[00:01:35]: 		K: 	spring	 V: 	default	
[00:01:35]: 		K: 	squid	 V: 	default	
[00:01:35]: 		K: 	stageplays	 V: 	default	
[00:01:35]: 		K: 	start_location	 V: 	default	
[00:01:35]: 		K: 	summer	 V: 	default	
[00:01:35]: 		K: 	summerhounds	 V: 	default	
[00:01:35]: 		K: 	tallbirds	 V: 	default	
[00:01:35]: 		K: 	task_set	 V: 	default	
[00:01:35]: 		K: 	temperaturedamage	 V: 	nonlethal	
[00:01:35]: 		K: 	tentacles	 V: 	default	
[00:01:35]: 		K: 	terrariumchest	 V: 	default	
[00:01:35]: 		K: 	touchstone	 V: 	default	
[00:01:35]: 		K: 	trees	 V: 	default	
[00:01:35]: 		K: 	tumbleweed	 V: 	default	
[00:01:35]: 		K: 	twiggytrees_regrowth	 V: 	default	
[00:01:35]: 		K: 	walrus	 V: 	default	
[00:01:35]: 		K: 	walrus_setting	 V: 	default	
[00:01:35]: 		K: 	wanderingtrader_enabled	 V: 	always	
[00:01:35]: 		K: 	wasps	 V: 	default	
[00:01:35]: 		K: 	weather	 V: 	default	
[00:01:35]: 		K: 	wildfires	 V: 	never	
[00:01:35]: 		K: 	winter	 V: 	default	
[00:01:35]: 		K: 	winterhounds	 V: 	default	
[00:01:35]: 		K: 	winters_feast	 V: 	default	
[00:01:35]: 		K: 	wobsters	 V: 	default	
[00:01:35]: 		K: 	world_size	 V: 	default	
[00:01:35]: 		K: 	wormhole_prefab	 V: 	wormhole	
[00:01:35]: 		K: 	year_of_the_beefalo	 V: 	default	
[00:01:35]: 		K: 	year_of_the_bunnyman	 V: 	default	
[00:01:35]: 		K: 	year_of_the_carrat	 V: 	default	
[00:01:35]: 		K: 	year_of_the_catcoon	 V: 	default	
[00:01:35]: 		K: 	year_of_the_dragonfly	 V: 	default	
[00:01:35]: 		K: 	year_of_the_gobbler	 V: 	default	
[00:01:35]: 		K: 	year_of_the_pig	 V: 	default	
[00:01:35]: 		K: 	year_of_the_snake	 V: 	default	
[00:01:35]: 		K: 	year_of_the_varg	 V: 	default	
[00:01:35]: 	K: 	playstyle	 V: 	relaxed	
[00:01:35]: 	K: 	random_set_pieces	 V: 	table: 0000000015433770	
[00:01:35]: 		K: 	1	 V: 	Sculptures_2	
[00:01:35]: 		K: 	2	 V: 	Sculptures_3	
[00:01:35]: 		K: 	3	 V: 	Sculptures_4	
[00:01:35]: 		K: 	4	 V: 	Sculptures_5	
[00:01:35]: 		K: 	5	 V: 	Chessy_1	
[00:01:35]: 		K: 	6	 V: 	Chessy_2	
[00:01:35]: 		K: 	7	 V: 	Chessy_3	
[00:01:35]: 		K: 	8	 V: 	Chessy_4	
[00:01:35]: 		K: 	9	 V: 	Chessy_5	
[00:01:35]: 		K: 	10	 V: 	Chessy_6	
[00:01:35]: 		K: 	11	 V: 	Maxwell1	
[00:01:35]: 		K: 	12	 V: 	Maxwell2	
[00:01:35]: 		K: 	13	 V: 	Maxwell3	
[00:01:35]: 		K: 	14	 V: 	Maxwell4	
[00:01:35]: 		K: 	15	 V: 	Maxwell6	
[00:01:35]: 		K: 	16	 V: 	Maxwell7	
[00:01:35]: 		K: 	17	 V: 	Warzone_1	
[00:01:35]: 		K: 	18	 V: 	Warzone_2	
[00:01:35]: 		K: 	19	 V: 	Warzone_3	
[00:01:35]: 	K: 	required_prefabs	 V: 	table: 0000000015433E00	
[00:01:35]: 		K: 	1	 V: 	multiplayer_portal	
[00:01:35]: 	K: 	required_setpieces	 V: 	table: 0000000015434850	
[00:01:35]: 		K: 	1	 V: 	Sculptures_1	
[00:01:35]: 		K: 	2	 V: 	Maxwell5	
[00:01:35]: 	K: 	settings_desc	 V: 	更轻松的游戏方式，更少受到来自世界的威胁。
饥饿、寒冷、过热和黑暗将不会杀死冒险家。
降低冒险家受到的伤害。永远可以在绚丽之门复活。	
[00:01:35]: 	K: 	settings_id	 V: 	RELAXED	
[00:01:35]: 	K: 	settings_name	 V: 	轻松	
[00:01:35]: 	K: 	substitutes	 V: 	table: 00000000154348F0	
[00:01:35]: 	K: 	version	 V: 	4	
[00:01:35]: 	K: 	worldgen_desc	 V: 	更轻松的游戏方式，更少受到来自世界的威胁。
饥饿、寒冷、过热和黑暗将不会杀死冒险家。
降低冒险家受到的伤害。永远可以在绚丽之门复活。	
[00:01:35]: 	K: 	worldgen_id	 V: 	RELAXED	
[00:01:35]: 	K: 	worldgen_name	 V: 	轻松	
[00:01:35]: Loaded and applied level data override from ../leveldataoverride.lua	
[00:01:35]: Overwriting savedata with level data file.	
[00:01:35]: Not applying world gen overrides.	
[00:01:35]: Collecting garbage...
[00:01:35]: lua_gc took 0.12 seconds
[00:01:35]: ~ShardLuaProxy()
[00:01:35]: ~cEventLeaderboardProxy()
[00:01:35]: ~ItemServerLuaProxy()
[00:01:35]: ~InventoryLuaProxy()
[00:01:35]: ~NetworkLuaProxy()
[00:01:35]: ~SimLuaProxy()
[00:01:35]: [Workshop] CancelDownloads for all pending downloads
[00:01:35]: lua_close took 0.11 seconds
[00:01:35]: ReleaseAll
[00:01:35]: ReleaseAll Finished
[00:01:35]: cGame::StartPlaying
[00:01:35]: AppVersion::GetArchitecture() x64
[00:01:35]: LOADING LUA
[00:01:35]: DoLuaFile scripts/main.lua
[00:01:35]: DoLuaFile loading buffer scripts/main.lua
[00:01:35]: Translator:LoadPOFile - loading file: scripts/languages/chinese_s.po	
[00:01:37]: AddPortMapping(10999, 10999, **************) failed with code 718 (ConflictInMappingEntry)
[00:01:37]: AddPortMapping(10999, 10999, **************) failed with code 718 (ConflictInMappingEntry)
[00:01:38]:   taskgrouplist:	default	联机版	
[00:01:38]:   taskgrouplist:	classic	经典	
[00:01:38]:   taskgrouplist:	cave_default	地下	
[00:01:38]:   taskgrouplist:	lavaarena_taskset	熔炉	
[00:01:38]:   taskgrouplist:	quagmire_taskset	暴食	
[00:01:38]: Running main.lua
	
[00:01:38]: loaded modindex	
[00:01:38]: ModIndex: Beginning normal load sequence.
	
[00:01:38]: SUCCESS: Loaded modoverrides.lua	
[00:01:38]: modoverrides.lua enabling thinking	
[00:01:38]: ModIndex:GetModsToLoad inserting moddir, 	thinking	
[00:01:38]: Could not load mod_config_data/modconfiguration_thinking	
[00:01:38]: ModIndex:SetTempModConfigData	
[00:01:38]: Setting temp mod config for mod 	thinking	
[00:01:38]: Could not load mod_config_data/modconfiguration_thinking_CLIENT	
[00:01:38]: Loading mod: thinking (Season Workshop (四季工坊)) Version:0.1.0	
[00:01:38]: applying configuration_options from modoverrides.lua to mod thinking	
[00:01:38]: Overriding mod thinking's option invasion_count with value 2	
[00:01:38]: Overriding mod thinking's option vfx_strength with value 1	
[00:01:38]: Overriding mod thinking's option altar_cooldown_days with value 5	
[00:01:38]: Overriding mod thinking's option invasion_respawn_days with value 2	
[00:01:38]: Overriding mod thinking's option cloak_strength with value 1	
[00:01:38]: Overriding mod thinking's option invasion_hp_mul with value 0.4	
[00:01:38]: Overriding mod thinking's option blade_fx_strength with value 1	
[00:01:38]: Overriding mod thinking's option invasion_enabled with value true	
[00:01:38]: Overriding mod thinking's option gust_duration with value medium	
[00:01:38]: Overriding mod thinking's option gust_frequency with value 2	
[00:01:38]: Overriding mod thinking's option boss_hp_mult with value 1	
[00:01:38]: Overriding mod thinking's option blade_stack_strength with value 1	
[00:01:38]: Overriding mod thinking's option invasion_loot_mul with value 0.5	
[00:01:38]: Overriding mod thinking's option boss_shield_absorb with value 0.8	
[00:01:38]: Mod: thinking (Season Workshop (四季工坊))	Loading modworldgenmain.lua	
[00:01:38]: Mod: thinking (Season Workshop (四季工坊))	  Mod had no modworldgenmain.lua. Skipping.	
[00:01:38]: Mod: thinking (Season Workshop (四季工坊))	Loading modmain.lua	
[00:01:38]: MOD ERROR: thinking (Season Workshop (四季工坊)): Mod: thinking (Season Workshop (四季工坊))	
[00:01:39]: Event data unavailable: lavaarena_event_server/lavaarena_achievement_quest_defs
[00:01:39]: [string "../mods/thinking/modmain.lua"]:13: variable 'AddRecipe2' is not declared
LUA ERROR stack traceback:
        =[C] in function 'error'
        scripts/strict.lua(23,1)
        ../mods/thinking/modmain.lua(13,1) in main chunk
        =[C] in function 'xpcall'
        scripts/util.lua(789,1) in function 'RunInEnvironment'
        scripts/mods.lua(603,1) in function 'InitializeModMain'
        scripts/mods.lua(577,1) in function 'LoadMods'
        scripts/main.lua(391,1) in function 'ModSafeStartup'
        scripts/main.lua(522,1)
        =[C] in function 'SetPersistentString'
        scripts/mainfunctions.lua(29,1) in function 'SavePersistentString'
        scripts/modindex.lua(119,1)
        =[C] in function 'GetPersistentString'
        scripts/modindex.lua(106,1) in function 'BeginStartupSequence'
        scripts/main.lua(521,1) in function 'callback'
        scripts/modindex.lua(735,1)
        =[C] in function 'GetPersistentString'
        scripts/modindex.lua(709,1) in function 'Load'
        scripts/main.lua(520,1) in main chunk
[00:01:39]: [string "../mods/thinking/modmain.lua"]:13: variable 'AddRecipe2' is not declared
LUA ERROR stack traceback:
        =[C] in function 'error'
        scripts/strict.lua(23,1)
        ../mods/thinking/modmain.lua(13,1) in main chunk
        =[C] in function 'xpcall'
        scripts/util.lua(789,1) in function 'RunInEnvironment'
        scripts/mods.lua(603,1) in function 'InitializeModMain'
        scripts/mods.lua(577,1) in function 'LoadMods'
        scripts/main.lua(391,1) in function 'ModSafeStartup'
        scripts/main.lua(522,1)
        =[C] in function 'SetPersistentString'
        scripts/mainfunctions.lua(29,1) in function 'SavePersistentString'
        scripts/modindex.lua(119,1)
        =[C] in function 'GetPersistentString'
        scripts/modindex.lua(106,1) in function 'BeginStartupSequence'
        scripts/main.lua(521,1) in function 'callback'
        scripts/modindex.lua(735,1)
        =[C] in function 'GetPersistentString'
        scripts/modindex.lua(709,1) in function 'Load'
        scripts/main.lua(520,1) in main chunk	
[00:01:39]: [string "scripts/mainfunctions.lua"]:1624: variable 'SetGlobalErrorWidget' is not declared
LUA ERROR stack traceback:
        =[C] in function 'error'
        scripts/strict.lua(23,1)
        scripts/mainfunctions.lua(1624,1)
        =[C] in function 'GetPersistentString'
        scripts/quagmire_recipebook.lua(54,1) in function 'Load'
        scripts/main.lua(410,1) in function 'ModSafeStartup'
        scripts/main.lua(522,1)
        =[C] in function 'SetPersistentString'
        scripts/mainfunctions.lua(29,1) in function 'SavePersistentString'
        scripts/modindex.lua(119,1)
        =[C] in function 'GetPersistentString'
        scripts/modindex.lua(106,1) in function 'BeginStartupSequence'
        scripts/main.lua(521,1) in function 'callback'
        scripts/modindex.lua(735,1)
        =[C] in function 'GetPersistentString'
        scripts/modindex.lua(709,1) in function 'Load'
        scripts/main.lua(520,1) in main chunk
[00:01:39]: Failed to load the cookbook!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "filters"] in JSON4Lua.decode_scanString at position 2 : 11	
[00:01:39]: Trying to apply online cache of cookbook data..	
[00:01:39]: Was a success, using preparedfoods values of:	
[00:01:39]: 	K: 	asparagussoup	 V: 	table: 000000005A44AE00	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44B0D0	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44ACC0	
[00:01:39]: 				K: 	1	 V: 	asparagus	
[00:01:39]: 				K: 	2	 V: 	asparagus	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	pomegranate	
[00:01:39]: 	K: 	baconeggs	 V: 	table: 000000005A44C0C0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44BCB0	
[00:01:39]: 	K: 	bonestew	 V: 	table: 000000005A44B8F0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44A540	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44ABD0	
[00:01:39]: 				K: 	1	 V: 	meat	
[00:01:39]: 				K: 	2	 V: 	meat	
[00:01:39]: 				K: 	3	 V: 	meat	
[00:01:39]: 				K: 	4	 V: 	meat	
[00:01:39]: 	K: 	bunnystew	 V: 	table: 000000005A44A0E0	
[00:01:39]: 		K: 	has_eaten	 V: 	false	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A449CD0	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44A3B0	
[00:01:39]: 				K: 	1	 V: 	froglegs	
[00:01:39]: 				K: 	2	 V: 	ice	
[00:01:39]: 				K: 	3	 V: 	ice	
[00:01:39]: 				K: 	4	 V: 	ice	
[00:01:39]: 	K: 	dragonpie	 V: 	table: 000000005A449DC0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A449D70	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A449190	
[00:01:39]: 				K: 	1	 V: 	carrot	
[00:01:39]: 				K: 	2	 V: 	carrot	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	dragonfruit	
[00:01:39]: 			K: 	2	 V: 	table: 000000005A449F00	
[00:01:39]: 				K: 	1	 V: 	dragonfruit	
[00:01:39]: 				K: 	2	 V: 	tomato	
[00:01:39]: 				K: 	3	 V: 	tomato	
[00:01:39]: 				K: 	4	 V: 	tomato	
[00:01:39]: 	K: 	frogglebunwich	 V: 	table: 000000005A44A950	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44B530	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44AB30	
[00:01:39]: 				K: 	1	 V: 	carrot	
[00:01:39]: 				K: 	2	 V: 	fishmeat_small	
[00:01:39]: 				K: 	3	 V: 	froglegs	
[00:01:39]: 				K: 	4	 V: 	froglegs	
[00:01:39]: 	K: 	hotchili	 V: 	table: 000000005A44B4E0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44A9A0	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44AFE0	
[00:01:39]: 				K: 	1	 V: 	monstermeat	
[00:01:39]: 				K: 	2	 V: 	monstermeat	
[00:01:39]: 				K: 	3	 V: 	pepper	
[00:01:39]: 				K: 	4	 V: 	pepper	
[00:01:39]: 	K: 	jammypreserves	 V: 	table: 000000005A44C6B0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44BE40	
[00:01:39]: 	K: 	kabobs	 V: 	table: 000000005A44CC00	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44BF30	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44C570	
[00:01:39]: 				K: 	1	 V: 	bird_egg	
[00:01:39]: 				K: 	2	 V: 	carrot	
[00:01:39]: 				K: 	3	 V: 	monstermeat	
[00:01:39]: 				K: 	4	 V: 	twigs	
[00:01:39]: 	K: 	meatballs	 V: 	table: 000000005A44B7B0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44AD10	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44C8E0	
[00:01:39]: 				K: 	1	 V: 	ice	
[00:01:39]: 				K: 	2	 V: 	ice	
[00:01:39]: 				K: 	3	 V: 	ice	
[00:01:39]: 				K: 	4	 V: 	meat	
[00:01:39]: 			K: 	2	 V: 	table: 000000005A44BDF0	
[00:01:39]: 				K: 	1	 V: 	ice	
[00:01:39]: 				K: 	2	 V: 	ice	
[00:01:39]: 				K: 	3	 V: 	ice	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	3	 V: 	table: 000000005A44CCA0	
[00:01:39]: 				K: 	1	 V: 	berries	
[00:01:39]: 				K: 	2	 V: 	berries	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	4	 V: 	table: 000000005A44CAC0	
[00:01:39]: 				K: 	1	 V: 	carrot	
[00:01:39]: 				K: 	2	 V: 	carrot	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	5	 V: 	table: 000000005A44BAD0	
[00:01:39]: 				K: 	1	 V: 	carrot	
[00:01:39]: 				K: 	2	 V: 	carrot	
[00:01:39]: 				K: 	3	 V: 	cutlichen	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	6	 V: 	table: 000000005A44CB10	
[00:01:39]: 				K: 	1	 V: 	cutlichen	
[00:01:39]: 				K: 	2	 V: 	cutlichen	
[00:01:39]: 				K: 	3	 V: 	cutlichen	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 	K: 	monsterlasagna	 V: 	table: 000000005A44B580	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44B210	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44AF40	
[00:01:39]: 				K: 	1	 V: 	monstermeat	
[00:01:39]: 				K: 	2	 V: 	monstermeat	
[00:01:39]: 				K: 	3	 V: 	monstermeat	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	2	 V: 	table: 000000005A44AA40	
[00:01:39]: 				K: 	1	 V: 	honey	
[00:01:39]: 				K: 	2	 V: 	honey	
[00:01:39]: 				K: 	3	 V: 	monstermeat	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 	K: 	pepperpopper	 V: 	table: 000000005A44D9C0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44DC40	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44DE70	
[00:01:39]: 				K: 	1	 V: 	meat	
[00:01:39]: 				K: 	2	 V: 	onion	
[00:01:39]: 				K: 	3	 V: 	onion	
[00:01:39]: 				K: 	4	 V: 	pepper	
[00:01:39]: 			K: 	2	 V: 	table: 000000005A44DF10	
[00:01:39]: 				K: 	1	 V: 	carrot	
[00:01:39]: 				K: 	2	 V: 	meat	
[00:01:39]: 				K: 	3	 V: 	onion	
[00:01:39]: 				K: 	4	 V: 	pepper	
[00:01:39]: 	K: 	perogies	 V: 	table: 000000005A44C7F0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44C2F0	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44CED0	
[00:01:39]: 				K: 	1	 V: 	bird_egg	
[00:01:39]: 				K: 	2	 V: 	bird_egg	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	2	 V: 	table: 000000005A44DEC0	
[00:01:39]: 				K: 	1	 V: 	bird_egg	
[00:01:39]: 				K: 	2	 V: 	blue_cap	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	3	 V: 	table: 000000005A44D1A0	
[00:01:39]: 				K: 	1	 V: 	bird_egg	
[00:01:39]: 				K: 	2	 V: 	bird_egg	
[00:01:39]: 				K: 	3	 V: 	blue_cap	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 			K: 	4	 V: 	table: 000000005A44D2E0	
[00:01:39]: 				K: 	1	 V: 	berries	
[00:01:39]: 				K: 	2	 V: 	bird_egg	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	monstermeat	
[00:01:39]: 	K: 	ratatouille	 V: 	table: 000000005A44C1B0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44C430	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44C020	
[00:01:39]: 				K: 	1	 V: 	garlic	
[00:01:39]: 				K: 	2	 V: 	garlic	
[00:01:39]: 				K: 	3	 V: 	garlic	
[00:01:39]: 				K: 	4	 V: 	potato	
[00:01:39]: 			K: 	2	 V: 	table: 000000005A44BB70	
[00:01:39]: 				K: 	1	 V: 	garlic	
[00:01:39]: 				K: 	2	 V: 	pepper	
[00:01:39]: 				K: 	3	 V: 	pepper	
[00:01:39]: 				K: 	4	 V: 	tomato	
[00:01:39]: 			K: 	3	 V: 	table: 000000005A44CBB0	
[00:01:39]: 				K: 	1	 V: 	corn	
[00:01:39]: 				K: 	2	 V: 	corn	
[00:01:39]: 				K: 	3	 V: 	corn	
[00:01:39]: 				K: 	4	 V: 	corn	
[00:01:39]: 			K: 	4	 V: 	table: 000000005A44C200	
[00:01:39]: 				K: 	1	 V: 	berries	
[00:01:39]: 				K: 	2	 V: 	berries	
[00:01:39]: 				K: 	3	 V: 	berries	
[00:01:39]: 				K: 	4	 V: 	carrot	
[00:01:39]: 			K: 	5	 V: 	table: 000000005A44C890	
[00:01:39]: 				K: 	1	 V: 	blue_cap	
[00:01:39]: 				K: 	2	 V: 	blue_cap	
[00:01:39]: 				K: 	3	 V: 	blue_cap	
[00:01:39]: 				K: 	4	 V: 	blue_cap	
[00:01:39]: 			K: 	6	 V: 	table: 000000005A44C2A0	
[00:01:39]: 				K: 	1	 V: 	berries	
[00:01:39]: 				K: 	2	 V: 	blue_cap	
[00:01:39]: 				K: 	3	 V: 	carrot	
[00:01:39]: 				K: 	4	 V: 	green_cap	
[00:01:39]: 	K: 	stuffedeggplant	 V: 	table: 000000005A44BD50	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44BDA0	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44BEE0	
[00:01:39]: 				K: 	1	 V: 	corn	
[00:01:39]: 				K: 	2	 V: 	corn	
[00:01:39]: 				K: 	3	 V: 	eggplant	
[00:01:39]: 				K: 	4	 V: 	garlic	
[00:01:39]: 	K: 	talleggs	 V: 	table: 000000005A44B1C0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44A860	
[00:01:39]: 	K: 	trailmix	 V: 	table: 000000005A44C5C0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44C840	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44C700	
[00:01:39]: 				K: 	1	 V: 	acorn	
[00:01:39]: 				K: 	2	 V: 	berries	
[00:01:39]: 				K: 	3	 V: 	berries	
[00:01:39]: 				K: 	4	 V: 	berries	
[00:01:39]: 	K: 	turkeydinner	 V: 	table: 000000005A44BFD0	
[00:01:39]: 		K: 	has_eaten	 V: 	true	
[00:01:39]: 		K: 	recipes	 V: 	table: 000000005A44B940	
[00:01:39]: 			K: 	1	 V: 	table: 000000005A44C980	
[00:01:39]: 				K: 	1	 V: 	carrot	
[00:01:39]: 				K: 	2	 V: 	drumstick	
[00:01:39]: 				K: 	3	 V: 	drumstick	
[00:01:39]: 				K: 	4	 V: 	meat	
[00:01:39]: Also using old stored filters values of:	
[00:01:39]: 	(empty)	
[00:01:39]: Saving cookbook file as a fixup.	
[00:01:39]: Failed to load the data in skilltree!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "skillxp"] in JSON4Lua.decode_scanString at position 2 : 11	
[00:01:39]: Trying to apply online cache of skilltree data..	
[00:01:39]: Was a success, using old stored XP values of:	
[00:01:39]: 	K: 	wolfgang	 V: 	50	
[00:01:39]: Also using old stored skill selection values of:	
[00:01:39]: 	K: 	wolfgang	 V: 	table: 00000000589CFA00	
[00:01:39]: 		K: 	wolfgang_autogym	 V: 	true	
[00:01:39]: 		K: 	wolfgang_critwork_1	 V: 	true	
[00:01:39]: 		K: 	wolfgang_normal_coach	 V: 	true	
[00:01:39]: 		K: 	wolfgang_overbuff_1	 V: 	true	
[00:01:39]: 		K: 	wolfgang_overbuff_2	 V: 	true	
[00:01:39]: 		K: 	wolfgang_overbuff_3	 V: 	true	
[00:01:39]: 		K: 	wolfgang_planardamage_1	 V: 	true	
[00:01:39]: 		K: 	wolfgang_planardamage_2	 V: 	true	
[00:01:39]: 		K: 	wolfgang_planardamage_3	 V: 	true	
[00:01:39]: 		K: 	wolfgang_planardamage_4	 V: 	true	
[00:01:39]: 		K: 	wolfgang_planardamage_5	 V: 	true	
[00:01:39]: Saving skilltree file as a fixup.	
[00:01:39]: Failed to load the data in generickv!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "kvs"] in JSON4Lua.decode_scanString at position 2 : 7	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "D9D27D00"] in JSON4Lua.decode_scanString at position 2 : 12	0	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "2EF75831"] in JSON4Lua.decode_scanString at position 2 : 12	1	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "19C004B2"] in JSON4Lua.decode_scanString at position 2 : 12	2	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "93186CB3"] in JSON4Lua.decode_scanString at position 2 : 12	3	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "69EB10D4"] in JSON4Lua.decode_scanString at position 2 : 12	4	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "4C2B8B15"] in JSON4Lua.decode_scanString at position 2 : 12	5	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "CF1626"] in JSON4Lua.decode_scanString at position 2 : 10	6	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "87CB6FD7"] in JSON4Lua.decode_scanString at position 2 : 12	7	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "9972AE98"] in JSON4Lua.decode_scanString at position 2 : 12	8	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "DF87DE19"] in JSON4Lua.decode_scanString at position 2 : 12	9	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "D639586A"] in JSON4Lua.decode_scanString at position 2 : 12	10	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "341774AB"] in JSON4Lua.decode_scanString at position 2 : 12	11	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "7E6D93AC"] in JSON4Lua.decode_scanString at position 2 : 12	12	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "7B49E62D"] in JSON4Lua.decode_scanString at position 2 : 12	13	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "5116571E"] in JSON4Lua.decode_scanString at position 2 : 12	14	
[00:01:39]: Failed to load the bucketdata in ScrapbookPartitions!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "E54929DF"] in JSON4Lua.decode_scanString at position 2 : 12	15	
[00:01:39]: Failed to load the loading tips!	false	[string "scripts/json.lua"]:450: Failed to load string [ return "shownloadingtips"] in JSON4Lua.decode_scanString at position 2 : 20	
[00:01:39]: DoLuaFile Error: (null)
[00:01:39]: LuaError but no error string
[00:01:39]: Error loading main.lua
[00:01:39]: Failed mSimulation->Reset()
[00:01:39]: [Workshop] CancelDownloads for all pending downloads
[00:01:39]: Collecting garbage...
[00:01:39]: lua_gc took 0.17 seconds
[00:01:39]: ~ShardLuaProxy()
[00:01:39]: ~cEventLeaderboardProxy()
[00:01:39]: ~ItemServerLuaProxy()
[00:01:39]: ~InventoryLuaProxy()
[00:01:39]: ~NetworkLuaProxy()
[00:01:39]: ~SimLuaProxy()
[00:01:39]: [Workshop] CancelDownloads for all pending downloads
[00:01:39]: lua_close took 0.04 seconds
[00:01:40]: [Workshop] CancelDownloads for all pending downloads
[00:01:40]: [Steam] Auth ticket cancelled
[00:01:40]: CurlRequestManager::ClientThread::Main() complete
[00:01:40]: HttpClient2 discarded 0 callbacks.
[00:01:40]: Shutting down
