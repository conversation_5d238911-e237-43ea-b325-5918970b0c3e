-- DST多人联机合规性检查工具
-- 基于官方文档和最佳实践，全面检查项目是否符合DST规范

local _G = GLOBAL
local TheWorld = _G.TheWorld
local TheNet = _G.TheNet

local DSTComplianceChecker = {}

-- 检查网络变量规范
function DSTComplianceChecker.CheckNetworkVariables()
    local issues = {}
    local warnings = {}
    local recommendations = {}
    
    -- 检查forest_network上的组件
    if TheWorld and TheWorld.net then
        local forest_network = TheWorld.net
        
        -- 检查组件是否正确挂载在forest_network上
        local expected_components = {
            "seasonal_gust_manager",
            "season_warden_invasion", 
            "season_engraving_network",
            "network_sync_manager"
        }
        
        for _, comp_name in ipairs(expected_components) do
            if not forest_network.components or not forest_network.components[comp_name] then
                table.insert(issues, "Missing component on forest_network: " .. comp_name)
            else
                -- 检查组件的网络变量
                local component = forest_network.components[comp_name]
                if component._network_initialized == false then
                    table.insert(warnings, "Component " .. comp_name .. " network not initialized")
                end
            end
        end
    else
        table.insert(issues, "forest_network not available")
    end
    
    return {
        valid = #issues == 0,
        issues = issues,
        warnings = warnings,
        recommendations = recommendations
    }
end

-- 检查服务端权威性
function DSTComplianceChecker.CheckServerAuthority()
    local issues = {}
    local warnings = {}
    
    if not TheWorld.ismastersim then
        table.insert(warnings, "Running on client - server authority check skipped")
        return {valid = true, issues = issues, warnings = warnings}
    end
    
    -- 检查关键组件是否只在服务端运行逻辑
    local server_only_components = {
        "seasonal_gust_manager",
        "season_warden_invasion",
        "season_engraving_network"
    }
    
    if TheWorld.net and TheWorld.net.components then
        for _, comp_name in ipairs(server_only_components) do
            local component = TheWorld.net.components[comp_name]
            if component then
                -- 检查是否有客户端逻辑泄露
                if component.client_logic_detected then
                    table.insert(issues, "Component " .. comp_name .. " has client logic on server")
                end
            end
        end
    end
    
    return {
        valid = #issues == 0,
        issues = issues,
        warnings = warnings
    }
end

-- 检查Replicas和Classifieds架构
function DSTComplianceChecker.CheckReplicasClassifieds()
    local issues = {}
    local warnings = {}
    local recommendations = {}
    
    -- 检查是否正确实现了replica/classified架构
    local players_checked = 0
    local replicas_found = 0
    local classifieds_found = 0
    
    for _, player in ipairs(_G.AllPlayers or {}) do
        if player and player:IsValid() then
            players_checked = players_checked + 1
            
            -- 检查replica组件
            if player.replica and player.replica.season_engraving then
                replicas_found = replicas_found + 1
            else
                table.insert(warnings, "Player missing season_engraving replica: " .. (player.prefab or "unknown"))
            end
            
            -- 检查classified实体
            if player.season_engraving_classified then
                classifieds_found = classifieds_found + 1
                
                -- 验证classified实体
                local classified = player.season_engraving_classified
                if not classified:IsValid() then
                    table.insert(issues, "Invalid classified entity for player: " .. (player.prefab or "unknown"))
                elseif not classified.components or not classified.components.season_engraving_classified then
                    table.insert(issues, "Classified entity missing component for player: " .. (player.prefab or "unknown"))
                end
            else
                table.insert(warnings, "Player missing season_engraving_classified: " .. (player.prefab or "unknown"))
            end
        end
    end
    
    if players_checked > 0 then
        table.insert(recommendations, string.format("Checked %d players: %d replicas, %d classifieds", 
            players_checked, replicas_found, classifieds_found))
    end
    
    return {
        valid = #issues == 0,
        issues = issues,
        warnings = warnings,
        recommendations = recommendations
    }
end

-- 检查RPC实现
function DSTComplianceChecker.CheckRPCImplementation()
    local issues = {}
    local warnings = {}
    local recommendations = {}
    
    -- 检查是否使用了官方RPC机制
    if _G.RPC and _G.RPC.SeasonWorkshop_SetSeasonEngraving then
        table.insert(recommendations, "Using official RPC mechanism for SeasonWorkshop")
    else
        table.insert(warnings, "Official RPC mechanism not detected - may be using custom implementation")
    end
    
    -- 检查RPC系统初始化状态
    local RPCSystem = require("scripts/utils/rpc_system")
    if RPCSystem and RPCSystem.initialized then
        table.insert(recommendations, "RPC system properly initialized")
    else
        table.insert(issues, "RPC system not initialized")
    end
    
    return {
        valid = #issues == 0,
        issues = issues,
        warnings = warnings,
        recommendations = recommendations
    }
end

-- 检查数据持久化
function DSTComplianceChecker.CheckDataPersistence()
    local issues = {}
    local warnings = {}
    
    -- 检查组件是否正确实现了OnSave/OnLoad
    local components_to_check = {
        "season_engraving",
        "seasonal_gust_manager",
        "season_warden_invasion"
    }
    
    for _, comp_name in ipairs(components_to_check) do
        local component_class = _G.require("scripts/components/" .. comp_name)
        if component_class then
            if not component_class.OnSave then
                table.insert(warnings, "Component " .. comp_name .. " missing OnSave method")
            end
            if not component_class.OnLoad then
                table.insert(warnings, "Component " .. comp_name .. " missing OnLoad method")
            end
        end
    end
    
    return {
        valid = #issues == 0,
        issues = issues,
        warnings = warnings
    }
end

-- 检查性能优化
function DSTComplianceChecker.CheckPerformanceOptimizations()
    local issues = {}
    local warnings = {}
    local recommendations = {}
    
    -- 检查网络变量节流
    if TheWorld and TheWorld.net and TheWorld.net.components then
        local network_manager = TheWorld.net.components.network_sync_manager
        if network_manager and network_manager.throttle_enabled then
            table.insert(recommendations, "Network throttling enabled")
        else
            table.insert(warnings, "Network throttling not detected")
        end
    end
    
    -- 检查错误处理
    local ErrorHandler = require("scripts/utils/error_handler")
    if ErrorHandler and ErrorHandler.initialized then
        table.insert(recommendations, "Error handling system active")
    else
        table.insert(warnings, "Error handling system not detected")
    end
    
    return {
        valid = #issues == 0,
        issues = issues,
        warnings = warnings,
        recommendations = recommendations
    }
end

-- 全面合规性检查
function DSTComplianceChecker.RunFullComplianceCheck()
    local results = {
        timestamp = os.time(),
        overall_compliant = true,
        checks = {}
    }
    
    -- 运行所有检查
    local checks = {
        {name = "Network Variables", func = DSTComplianceChecker.CheckNetworkVariables},
        {name = "Server Authority", func = DSTComplianceChecker.CheckServerAuthority},
        {name = "Replicas/Classifieds", func = DSTComplianceChecker.CheckReplicasClassifieds},
        {name = "RPC Implementation", func = DSTComplianceChecker.CheckRPCImplementation},
        {name = "Data Persistence", func = DSTComplianceChecker.CheckDataPersistence},
        {name = "Performance Optimizations", func = DSTComplianceChecker.CheckPerformanceOptimizations}
    }
    
    for _, check in ipairs(checks) do
        local result = check.func()
        result.name = check.name
        table.insert(results.checks, result)
        
        if not result.valid then
            results.overall_compliant = false
        end
    end
    
    return results
end

-- 生成合规性报告
function DSTComplianceChecker.GenerateComplianceReport()
    local results = DSTComplianceChecker.RunFullComplianceCheck()
    
    print("=== DST多人联机合规性检查报告 ===")
    print("检查时间:", os.date("%Y-%m-%d %H:%M:%S", results.timestamp))
    print("总体合规状态:", results.overall_compliant and "✅ 合规" or "❌ 不合规")
    print("")
    
    for _, check in ipairs(results.checks) do
        print(string.format("【%s】 %s", check.name, check.valid and "✅ 通过" or "❌ 失败"))
        
        if #check.issues > 0 then
            print("  问题:")
            for _, issue in ipairs(check.issues) do
                print("    ❌ " .. issue)
            end
        end
        
        if #check.warnings > 0 then
            print("  警告:")
            for _, warning in ipairs(check.warnings) do
                print("    ⚠️ " .. warning)
            end
        end
        
        if check.recommendations and #check.recommendations > 0 then
            print("  建议:")
            for _, rec in ipairs(check.recommendations) do
                print("    💡 " .. rec)
            end
        end
        
        print("")
    end
    
    return results
end

return DSTComplianceChecker
