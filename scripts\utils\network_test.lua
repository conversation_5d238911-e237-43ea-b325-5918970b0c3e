-- 网络同步测试工具
-- 用于测试DST多人联机的网络同步功能

local _G = GLOBAL
local TheWorld = _G.TheWorld
local TheNet = _G.TheNet

local NetworkTest = {}

-- 测试网络变量基本功能
function NetworkTest.TestBasicNetworkVariables()
    local results = {
        timestamp = os.time(),
        tests = {},
        overall_success = true
    }

    -- 测试1：forest_network可用性
    local test1 = {
        name = "Forest Network Availability",
        success = false,
        message = ""
    }

    if TheWorld and TheWorld.net then
        test1.success = true
        test1.message = "forest_network available"
    else
        test1.success = false
        test1.message = "forest_network not available"
        results.overall_success = false
    end
    table.insert(results.tests, test1)

    -- 测试2：组件挂载检查
    local test2 = {
        name = "Component Mounting",
        success = false,
        message = ""
    }

    if TheWorld and TheWorld.net and TheWorld.net.components then
        local components = TheWorld.net.components
        local required = {"seasonal_gust_manager", "season_warden_invasion", "season_engraving_network", "network_sync_manager"}
        local found = {}

        for _, comp_name in ipairs(required) do
            if components[comp_name] then
                table.insert(found, comp_name)
            end
        end
        
        if #found == #required then
            test2.success = true
            test2.message = "All components mounted: " .. table.concat(found, ", ")
        else
            test2.success = false
            test2.message = "Missing components: " .. (#required - #found) .. "/" .. #required
            results.overall_success = false
        end
    else
        test2.success = false
        test2.message = "forest_network.components not available"
        results.overall_success = false
    end
    table.insert(results.tests, test2)

    -- 测试3：网络变量初始化
    local test3 = {
        name = "Network Variable Initialization",
        success = false,
        message = ""
    }

    if TheWorld and TheWorld.net and TheWorld.net.components then
        local gust_manager = TheWorld.net.components.seasonal_gust_manager
        if gust_manager then
            if gust_manager._network_initialized then
                test3.success = true
                test3.message = "Network variables initialized"
            else
                test3.success = false
                test3.message = "Network variables not initialized"
                results.overall_success = false
            end
        else
            test3.success = false
            test3.message = "Gust manager not found"
            results.overall_success = false
        end
    else
        test3.success = false
        test3.message = "Components not available"
        results.overall_success = false
    end
    table.insert(results.tests, test3)

    return results
end

-- 测试网络同步功能
function NetworkTest.TestNetworkSync()
    if not TheWorld.ismastersim then
        return false, "Network sync test can only run on master sim"
    end

    local results = {
        timestamp = os.time(),
        sync_tests = {},
        overall_success = true
    }

    -- 测试季风乱流网络同步
    local gust_test = {
        name = "Seasonal Gust Network Sync",
        success = false,
        message = ""
    }

    if TheWorld.net and TheWorld.net.components and TheWorld.net.components.seasonal_gust_manager then
        local gust_manager = TheWorld.net.components.seasonal_gust_manager
        
        if gust_manager._net_active and gust_manager._net_season then
            -- 测试设置网络变量
            local test_success = pcall(function()
                gust_manager._net_active:set(true)
                gust_manager._net_season:set("test")
            end)
            
            if test_success then
                -- 验证值是否正确设置
                local active_value = gust_manager._net_active:value()
                local season_value = gust_manager._net_season:value()
                
                if active_value == true and season_value == "test" then
                    gust_test.success = true
                    gust_test.message = "Network sync working correctly"
                    
                    -- 清理测试数据
                    gust_manager._net_active:set(false)
                    gust_manager._net_season:set("")
                else
                    gust_test.success = false
                    gust_test.message = "Network values not syncing correctly"
                    results.overall_success = false
                end
            else
                gust_test.success = false
                gust_test.message = "Failed to set network variables"
                results.overall_success = false
            end
        else
            gust_test.success = false
            gust_test.message = "Network variables not available"
            results.overall_success = false
        end
    else
        gust_test.success = false
        gust_test.message = "Gust manager not available"
        results.overall_success = false
    end
    
    table.insert(results.sync_tests, gust_test)

    return results.overall_success, results
end

-- 测试网络延迟容错
function NetworkTest.TestNetworkLatencyTolerance()
    local results = {
        timestamp = os.time(),
        latency_tests = {},
        overall_success = true
    }

    -- 这个测试主要检查客户端是否正确处理网络延迟
    local latency_test = {
        name = "Network Latency Tolerance",
        success = false,
        message = ""
    }

    -- 检查客户端事件监听器是否正确设置
    if TheWorld.net and TheWorld.net.components then
        local gust_manager = TheWorld.net.components.seasonal_gust_manager
        if gust_manager then
            -- 检查是否有适当的错误处理
            if gust_manager._network_initialized ~= nil then
                latency_test.success = true
                latency_test.message = "Network latency tolerance mechanisms in place"
            else
                latency_test.success = false
                latency_test.message = "Network initialization flag missing"
                results.overall_success = false
            end
        else
            latency_test.success = false
            latency_test.message = "Gust manager not available for latency test"
            results.overall_success = false
        end
    else
        latency_test.success = false
        latency_test.message = "Network components not available"
        results.overall_success = false
    end

    table.insert(results.latency_tests, latency_test)

    return results.overall_success, results
end

-- 运行完整的网络测试套件
function NetworkTest.RunFullTestSuite()
    print("=== Season Workshop 网络测试套件 ===")
    print("开始时间: " .. os.date("%Y-%m-%d %H:%M:%S"))
    
    local all_results = {
        timestamp = os.time(),
        basic_tests = {},
        sync_tests = {},
        latency_tests = {},
        overall_success = true
    }

    -- 运行基本测试
    print("\n--- 基本网络变量测试 ---")
    local basic_results = NetworkTest.TestBasicNetworkVariables()
    all_results.basic_tests = basic_results
    
    for _, test in ipairs(basic_results.tests) do
        local status = test.success and "✅" or "❌"
        print(status .. " " .. test.name .. ": " .. test.message)
    end
    
    if not basic_results.overall_success then
        all_results.overall_success = false
    end

    -- 运行同步测试（仅在服务端）
    if TheWorld.ismastersim then
        print("\n--- 网络同步测试 ---")
        local sync_success, sync_results = NetworkTest.TestNetworkSync()
        all_results.sync_tests = sync_results
        
        if sync_success then
            print("✅ 网络同步测试通过")
        else
            print("❌ 网络同步测试失败")
            all_results.overall_success = false
            
            for _, test in ipairs(sync_results.sync_tests) do
                local status = test.success and "✅" or "❌"
                print("  " .. status .. " " .. test.name .. ": " .. test.message)
            end
        end
    else
        print("\n--- 网络同步测试 ---")
        print("⏭️ 跳过（仅在服务端运行）")
    end

    -- 运行延迟容错测试
    print("\n--- 网络延迟容错测试 ---")
    local latency_success, latency_results = NetworkTest.TestNetworkLatencyTolerance()
    all_results.latency_tests = latency_results
    
    if latency_success then
        print("✅ 网络延迟容错测试通过")
    else
        print("❌ 网络延迟容错测试失败")
        all_results.overall_success = false
        
        for _, test in ipairs(latency_results.latency_tests) do
            local status = test.success and "✅" or "❌"
            print("  " .. status .. " " .. test.name .. ": " .. test.message)
        end
    end

    -- 总结
    print("\n=== 测试总结 ===")
    if all_results.overall_success then
        print("✅ 所有网络测试通过")
        print("🎉 Season Workshop 网络同步功能正常")
    else
        print("❌ 部分测试失败")
        print("🔧 建议运行网络修复工具: RepairSeasonWorkshopNetwork()")
    end
    
    print("结束时间: " .. os.date("%Y-%m-%d %H:%M:%S"))
    
    return all_results
end

return NetworkTest
